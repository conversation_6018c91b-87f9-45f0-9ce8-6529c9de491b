import React, {
    useEffect,
    useRef,
    useState,
    useCallback,
    useMemo,
} from "react";
import {
    Dimensions,
    Text,
    TouchableOpacity,
    View,
    Animated,
    Easing,
} from "react-native";
import { useTheme } from "@context/theme";
import { Audio } from "expo-av";
import { PauseIcon, PlayIcon } from "react-native-heroicons/mini";
import {
    AudioPlayController,
    enAudioState,
} from "@app/context/AudioPlayController";
import { useServerUrl } from "@context/server";
import { MicrophoneIcon } from "react-native-heroicons/outline";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import ProfilePicture from "@components/profile_picture";
import Slider from "@react-native-community/slider";
import Loading from "../loading";
import { changeOpacity } from "@utils/theme";
import MediaMessageMetadata from "@components/media_message_metadata";

import type UserModel from '@typings/database/models/servers/user';
import type PostModel from '@typings/database/models/servers/post';

// generate random bar heights per-instance - memoized to prevent regeneration
const makeLevels = (n: number) =>
    Array.from({ length: n }, () => Math.floor(Math.random() * 11) + 12);

type AudioFileProps = {
    disabled?: boolean;
    file: FileInfo;
    isCurrentUser?: boolean;
    author?: any;
    acknolowgment?: string | React.JSX.Element;
    // New props for timestamp and read receipts
    post?: PostModel;
    currentUser?: UserModel;
    acknowledgementsVisible?: boolean;
    hasReactions?: boolean;
    location?: string;
    showTimestamp?: boolean;
    showReadReceipts?: boolean;
};

const AudioFile: React.FC<AudioFileProps> = React.memo(
    ({
        disabled,
        file,
        isCurrentUser = false,
        author,
        acknolowgment,
        post,
        currentUser,
        acknowledgementsVisible = false,
        hasReactions = false,
        location = '',
        showTimestamp = true,
        showReadReceipts = true,
    }) => {
        const theme = useTheme();
        const serverUrl = useServerUrl();
        const audioCtrl = AudioPlayController();
        const windowWidth = Dimensions.get("window").width;
        const defaultWidth = windowWidth < 400 ? 290 : 306;
        const waveformWidth = defaultWidth - 96; // width for the slider
        // Reduce playback head movement range to prevent overlap with profile image
        // Avatar is 50px + 5px margin = 55px, so reduce movement by additional 20px for safe spacing
        const playbackHeadMaxWidth = waveformWidth - 20;

        // --- controller state ---
        const {
            playAudio,
            playingState,
            currentTrack,
            currentPosition,
            seekToPosition,
            fileDuration: controllerFileDuration,
        } = audioCtrl;

        const isActive = currentTrack === file.id;
        const isPlaying = isActive && playingState === enAudioState.playing;
        const isLoading = isActive && playingState === enAudioState.loading;

        // --- component state ---
        const [fileDuration, setFileDuration] = useState(0);
        const [countdown, setCountdown] = useState("0:00");

        // --- per-instance state for isolated animations ---
        const [localPosition, setLocalPosition] = useState(0);
        const [isDragging, setIsDragging] = useState(false);

        // --- per-instance animated values for waveform bars & text fade ---
        // Memoize the bar levels to prevent regeneration on re-renders
        const barLevels = useMemo(() => makeLevels(20), []);
        const barScales = useRef(
            barLevels.map(() => new Animated.Value(1))
        ).current;
        const textFade = useRef(new Animated.Value(0.5)).current;
        const lastBar = useRef(-1);

        // --- utility ---
        const fmt = (ms: number) => {
            if (ms <= 0) return "0:00";
            const s = Math.floor(ms / 1000);
            return `${Math.floor(s / 60)}:${(s % 60)
                .toString()
                .padStart(2, "0")}`;
        };

        // load duration
        useEffect(() => {
            (async () => {
                try {
                    const { sound } = await Audio.Sound.createAsync(
                        { uri: `${serverUrl}/api/v4/files/${file.id}` },
                        { shouldPlay: false }
                    );
                    const st = await sound.getStatusAsync();
                    if (st.isLoaded && st.durationMillis) {
                        setFileDuration(st.durationMillis);
                        setCountdown(fmt(st.durationMillis));
                    }
                    await sound.unloadAsync();
                } catch {
                    setFileDuration(0);
                    setCountdown("0:00");
                }
            })();
        }, [file.id]);

        // fade duration text
        useEffect(() => {
            Animated.timing(textFade, {
                toValue: isPlaying ? 1 : 0.5,
                duration: isPlaying ? 500 : 300,
                easing: Easing.out(Easing.ease),
                useNativeDriver: true,
            }).start();
        }, [isPlaying]);

        // Sync local position with global controller position for this specific track
        // Update more frequently for better responsiveness
        useEffect(() => {
            if (isActive && !isDragging) {
                setLocalPosition(currentPosition);
            }
        }, [isActive, currentPosition, isDragging]);

        // Additional effect for more frequent position updates during playback
        useEffect(() => {
            if (!isActive || !isPlaying || isDragging) return;

            const interval = setInterval(() => {
                // Get the most current position for immediate updates
                setLocalPosition(currentPosition);
            }, 50); // Update every 50ms for smoother animation

            return () => clearInterval(interval);
        }, [isActive, isPlaying, isDragging, currentPosition]);

        // update waveform & countdown in real time - only for active track
        useEffect(() => {
            if (!isActive || !isPlaying) return;

            let raf: number;
            const update = () => {
                const dur = controllerFileDuration || fileDuration;
                if (dur <= 0) return;

                // Use the current position directly for immediate updates
                const pos = localPosition;
                const pct = pos / dur;

                // Update countdown immediately
                setCountdown(fmt(Math.max(0, dur - pos)));

                // animate bars up to current index - only for this specific component
                const barCount = barScales.length;
                const idx = Math.floor(pct * barCount);
                if (idx !== lastBar.current) {
                    lastBar.current = idx;
                    barScales.forEach((bv, i) => {
                        if (i <= idx) {
                            Animated.sequence([
                                Animated.timing(bv, {
                                    toValue: 1.4,
                                    duration: 80, // Reduced duration for faster response
                                    easing: Easing.out(Easing.back(1.2)),
                                    useNativeDriver: true,
                                }),
                                Animated.timing(bv, {
                                    toValue: 1,
                                    duration: 120, // Reduced duration for faster response
                                    easing: Easing.out(Easing.quad),
                                    useNativeDriver: true,
                                }),
                            ]).start();
                        }
                    });
                }

                raf = requestAnimationFrame(update);
            };

            raf = requestAnimationFrame(update);
            return () => cancelAnimationFrame(raf);
        }, [
            isActive,
            isPlaying,
            localPosition,
            fileDuration,
            controllerFileDuration,
            barScales,
        ]);

        // Helper function to reset this specific voice message state
        const resetVoiceMessageState = useCallback((duration: number) => {
            if (duration > 0) {
                // Reset countdown timer to full duration for this specific instance
                setCountdown(fmt(duration));

                // Reset animations to initial state for this specific instance
                barScales.forEach((bv) => bv.setValue(1));
                lastBar.current = -1;

                // Reset position for this specific instance
                setLocalPosition(0);
            }
        }, [barScales]);

        // Track previous state to detect transitions for this specific component
        const prevPlayingState = useRef(playingState);
        const prevIsActive = useRef(isActive);

        // Reset logic - only affects this specific voice message instance
        useEffect(() => {
            const currentDuration = controllerFileDuration || fileDuration;

            // Only reset if this specific voice message's state changed
            if (isActive) {
                // Case 1: Audio completion (natural end) - only for this specific track
                if (playingState === enAudioState.complate && prevPlayingState.current !== enAudioState.complate) {
                    resetVoiceMessageState(currentDuration);
                }
                // Case 2: Manual stop/pause - only for this specific track
                else if (
                    !isPlaying &&
                    [enAudioState.none, enAudioState.puse].includes(playingState) &&
                    prevPlayingState.current === enAudioState.playing
                ) {
                    // For pause, don't reset position - keep current position for resume
                    if (playingState === enAudioState.puse) {
                        setCountdown(fmt(Math.max(0, currentDuration - localPosition)));
                        barScales.forEach((bv) => bv.setValue(1));
                        lastBar.current = -1;
                    } else {
                        // For stop, reset everything
                        resetVoiceMessageState(currentDuration);
                    }
                }
            } else {
                // Case 3: This track becomes inactive (another track starts playing)
                // Only reset if this track was previously active
                if (prevIsActive.current === true) {
                    resetVoiceMessageState(currentDuration);
                }
            }

            // Update previous state references
            prevPlayingState.current = playingState;
            prevIsActive.current = isActive;
        }, [
            isActive,
            isPlaying,
            playingState,
            fileDuration,
            controllerFileDuration,
            localPosition,
            resetVoiceMessageState,
        ]);

        // play/pause/replay
        const onPress = useCallback(async () => {
            if (isActive && playingState === enAudioState.complate) {
                if (file.id) {
                    await playAudio({ url: file.id }, 0);
                }
            } else {
                if (file.id) {
                    await playAudio({ url: file.id }, localPosition || 0);
                }
            }
        }, [
            isActive,
            playingState,
            currentPosition,
            controllerFileDuration,
            fileDuration,
            file.id,
            playAudio,
        ]);

        // bar color - use local position for accurate synchronization
        const barColor = useCallback(
            (i: number) => {
                const base = isCurrentUser
                    ? changeOpacity(theme.sidebarText, 0.4)
                    : "#ccc";
                if (!isActive) return base;
                const dur = controllerFileDuration || fileDuration;
                if (dur <= 0) return base;
                const pct = (localPosition / dur) * 100;
                return pct >= (i / barScales.length) * 100
                    ? isCurrentUser
                        ? "white"
                        : theme.buttonBg
                    : base;
            },
            [
                isActive,
                localPosition,
                controllerFileDuration,
                fileDuration,
                isCurrentUser,
                theme.buttonBg,
                theme.sidebarText,
                barScales.length,
            ]
        );

        // play/pause icon
        const icon = useMemo(() => {
            if (isLoading)
                return (
                    <Loading
                        size={28}
                        color={isCurrentUser ? "white" : theme.buttonBg}
                    />
                );
            return isPlaying ? (
                <PauseIcon
                    size={25}
                    color={isCurrentUser ? "white" : "#808887"}
                />
            ) : (
                <PlayIcon
                    size={25}
                    color={isCurrentUser ? "white" : "#808887"}
                />
            );
        }, [isPlaying, isLoading, isCurrentUser, theme.buttonBg]);

        return (
            <GestureHandlerRootView>
                <View
                    style={{
                        flexDirection: "row-reverse",
                        alignItems: "center",
                        justifyContent: "space-between",
                        width: defaultWidth,
                        height: 60,
                        paddingEnd: 10,
                    }}
                >
                    {/* Avatar */}
                    <View
                        style={{
                            width: 50,
                            height: 50,
                            borderRadius: 30,
                            overflow: "hidden",
                            marginTop: 10,
                            marginLeft: 5,
                        }}
                    >
                        {author ? (
                            <ProfilePicture
                                author={author}
                                size={50}
                                showStatus={false}
                            />
                        ) : (
                            <View
                                style={{
                                    width: 50,
                                    height: 50,
                                    backgroundColor: isCurrentUser
                                        ? changeOpacity(
                                              theme.centerChannelBg,
                                              0.3
                                          )
                                        : changeOpacity(theme.sidebarText, 0.3),
                                    borderRadius: 27.5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <MicrophoneIcon
                                    size={25}
                                    color={
                                        isCurrentUser
                                            ? "white"
                                            : theme.centerChannelBg
                                    }
                                />
                            </View>
                        )}
                    </View>

                    {/* Mic overlay */}
                    <View
                        style={{ position: "absolute", bottom: -1, start: 225 }}
                    >
                        <MicrophoneIcon
                            size={20}
                            color={
                                isCurrentUser
                                    ? "white"
                                    : changeOpacity(theme.sidebarText, 0.3)
                            }
                        />
                    </View>

                    {/* Waveform + Slider-dot */}
                    <View style={{ flex: 1, marginHorizontal: 10, top: -13 }}>
                        <View style={{ position: "relative", top: 25 }}>
                            <View
                                style={{
                                    flexDirection: "row",
                                    justifyContent: "space-between",
                                }}
                            >
                                {barScales.map((bv, i) => (
                                    <Animated.View
                                        key={i}
                                        style={{
                                            width: 4,
                                            height: barLevels[i],
                                            borderRadius: 5,
                                            backgroundColor: barColor(i),
                                            transform: [{ scaleY: bv }],
                                        }}
                                    />
                                ))}
                            </View>

                            {/* Slider thumb as the dot */}
                            <Slider
                                style={{
                                    position: "absolute",
                                    top: -5,
                                    start: -11,
                                    width: waveformWidth,
                                    height: 30,
                                    zIndex: 50,
                                }}
                                minimumValue={0}
                                maximumValue={100}
                                value={
                                    (controllerFileDuration || fileDuration) > 0
                                        ? (localPosition /
                                              (controllerFileDuration ||
                                                  fileDuration)) *
                                          100
                                        : 0
                                }
                                minimumTrackTintColor="transparent"
                                maximumTrackTintColor="transparent"
                                thumbTintColor="transparent" // Hide default thumb to use custom dot
                                onValueChange={(pct) => {
                                    // live feedback while dragging
                                    setIsDragging(true);
                                    const dur =
                                        controllerFileDuration || fileDuration;
                                    const newPos = dur * (pct / 100);
                                    setLocalPosition(newPos);
                                    setCountdown(
                                        fmt(Math.max(0, dur - newPos))
                                    );
                                }}
                                onSlidingComplete={(pct) => {
                                    const dur =
                                        controllerFileDuration || fileDuration;
                                    const newTime = dur * (pct / 100);
                                    setIsDragging(false);
                                    if (isActive) {
                                        seekToPosition(newTime);
                                    }
                                    setLocalPosition(newTime);
                                    setCountdown(
                                        fmt(Math.max(0, dur - newTime))
                                    );
                                }}
                            />

                            {/* Custom larger playback head dot */}
                            <View
                                style={{
                                    position: "absolute",
                                    top: 1,
                                    left: ((controllerFileDuration || fileDuration) > 0
                                        ? (localPosition / (controllerFileDuration || fileDuration)) * playbackHeadMaxWidth
                                        : 0) - 8, // Center the dot (16px width / 2)
                                    width: 18,
                                    height: 18,
                                    borderRadius: 10,
                                    backgroundColor: isCurrentUser ? "white" : theme.buttonBg,
                                    borderWidth: 2,
                                    borderColor: isCurrentUser ? theme.buttonBg : "white",
                                    zIndex: 100,
                                    pointerEvents: "none", // Allow touch events to pass through to slider
                                }}
                            />
                        </View>

                        {/* Countdown and Metadata Container */}
                        <View
                            style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                alignItems: "center",
                                marginTop: 8,
                                top: 18,
                                right: 20,
                                paddingHorizontal: 8,
                                width: "100%", // Use full width for proper layout
                            }}
                        >
                            {/* Countdown Timer */}
                            <Animated.Text
                                style={{
                                    color: isCurrentUser ? "white" : "#808887",
                                    fontSize: 14,
                                    opacity: textFade,
                                }}
                            >
                                {countdown}
                            </Animated.Text>

                            {/* Timestamp and Read Receipts */}
                            {post && (
                                <MediaMessageMetadata
                                    post={post}
                                    currentUser={currentUser}
                                    isCurrentUser={isCurrentUser}
                                    acknowledgementsVisible={acknowledgementsVisible}
                                    hasReactions={hasReactions}
                                    location={location}
                                    showTimestamp={showTimestamp}
                                    showReadReceipts={showReadReceipts}
                                    textColor={isCurrentUser ? "white" : changeOpacity(theme.sidebarText, 0.7)}
                                    iconColor={isCurrentUser ? "white" : theme.sidebarText}
                                    iconSize={16}
                                    containerStyle={{
                                        backgroundColor: changeOpacity('black', 0.6),
                                                                        borderRadius: 8,
                                                                        paddingHorizontal: 6,
                                                                        paddingVertical: 2,
                                    }}
                                />
                            )}
                        </View>

                        {acknolowgment && typeof acknolowgment === "string" && (
                            <Text
                                style={{
                                    color: isCurrentUser ? "white" : "#808887",
                                }}
                            >
                                {acknolowgment}
                            </Text>
                        )}
                    </View>

                    {/* Play/Pause */}
                    <TouchableOpacity
                        disabled={disabled || isLoading}
                        onPress={onPress}
                        style={{
                            width: 30,
                            height: 30,
                            marginRight: 1,
                            marginStart: 10,
                            borderRadius: 20,
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        {icon}
                    </TouchableOpacity>
                </View>
            </GestureHandlerRootView>
        );
    }
);

export default AudioFile;
