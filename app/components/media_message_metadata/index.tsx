// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import { View, Text, I18nManager } from 'react-native';

import FormattedTime from '@components/formatted_time';
import Acknowledgements from '@components/post_list/post/body/acknowledgements';
import { useTheme } from '@context/theme';
import { getUserTimezone } from '@utils/user';
import { changeOpacity } from '@utils/theme';
import { typography } from '@utils/typography';

import type PostModel from '@typings/database/models/servers/post';
import type UserModel from '@typings/database/models/servers/user';

type MediaMessageMetadataProps = {
    post: PostModel;
    currentUser?: UserModel;
    isCurrentUser: boolean;
    acknowledgementsVisible?: boolean;
    hasReactions?: boolean;
    location: string;
    iconColor?: string;
    iconSize?: number;
    textColor?: string;
    containerStyle?: any;
    showTimestamp?: boolean;
    showReadReceipts?: boolean;
};

const MediaMessageMetadata: React.FC<MediaMessageMetadataProps> = ({
    post,
    currentUser,
    isCurrentUser,
    acknowledgementsVisible = false,
    hasReactions = false,
    location,
    iconColor,
    iconSize = 14,
    textColor,
    containerStyle,
    showTimestamp = true,
    showReadReceipts = true,
}) => {
    const theme = useTheme();
    const isRTL = I18nManager.isRTL;

    // Determine colors based on user type and theme
    const defaultTextColor = textColor || (isCurrentUser ? "white" : changeOpacity(theme.sidebarText, 0.7));
    const defaultIconColor = iconColor || (isCurrentUser ? "white" : theme.sidebarText);

    // Create timestamp component
    const timestampComponent = showTimestamp && currentUser ? (
        <FormattedTime
            timezone={getUserTimezone(currentUser)}
            isMilitaryTime={false}
            value={post.createAt}
            style={{
                color: defaultTextColor,
                ...typography("Body", 75, "Regular"),
                fontSize: 12,
                fontFamily: "IBMPlexSansArabic-Light",
            }}
        />
    ) : null;

    // Create read receipts component
    const readReceiptsComponent = showReadReceipts && acknowledgementsVisible && currentUser ? (
        <Acknowledgements
            currentUserId={currentUser.id}
            currentUserTimezone={currentUser.timezone}
            iconColor={defaultIconColor}
            hasReactions={hasReactions}
            location={location}
            post={post}
            theme={theme}
            iconSize={iconSize}
        />
    ) : null;

    // Don't render if no content to show
    if (!timestampComponent && !readReceiptsComponent) {
        return null;
    }

    return (
        <View
            style={[
                {
                    flexDirection: isRTL ? "row-reverse" : "row",
                    alignItems: "center",
                    justifyContent: "flex-end",
                    gap: 4,
                },
                containerStyle,
            ]}
        >
            {timestampComponent}
            {readReceiptsComponent}
        </View>
    );
};

export default MediaMessageMetadata;
